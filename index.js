const fs = require("fs");
const path = require("path");
const { NudgeEngine } = require("./src/nudgeEngine");
const { SleepMealTimingRule } = require("./rules/index");
const { createOSClient, connectMongoDB } = require("./utils/connection");
const { computeTargetAchievement } = require("./service/target_achieved");
const { getAllDefaultTrackers } = require("./service/trackers");
const { log: logger } = require("./utils/logger");
const { getTimeRangeByDate, getAdjustedDate, getLocalDateString } = require("./utils/helpers");
const { getUTCOffsetValue } = require("./service/userProfile");

const { upsertUserRecommendations } = require("./service/recommendations");
const { getActiveWindowData } = require("./service/targetCron");
const { getTargetsAchievedByDateRange } = require("./service/target_achieved");
const { getLatestTargetById } = require("./service/targets");
const { getStaticTargetsMap } = require("./utils/staticData");

const sleepTargetId = 4;
const mealLogTargetId = 15;

const defaultHeaders = {
  "Access-Control-Allow-Headers":
    "Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, x-api-key, X-Install-Type",
  "Access-Control-Allow-Origin": "*",
};

const ruleRegistry = {
  sleep_meal_gap: SleepMealTimingRule,
};

async function loadRuleConfigs() {
  const configPath = path.resolve(
    __dirname,
    "config/nudgeRules.json"
  );
  const data = fs.readFileSync(configPath, "utf-8");
  return JSON.parse(data);
}

async function instantiateRules() {
  const configs = await loadRuleConfigs();
  return configs.filter((cfg) => cfg.enabled && ruleRegistry[cfg.ruleId]).map((cfg) => new ruleRegistry[cfg.ruleId](cfg.params));
}

exports.handler = async (event) => {
  const body = JSON.parse(event.Records[0].body);
  logger.info(`Received request body: ${JSON.stringify(body)}`);
  const { userId, date, trackerIdDeviceIdMapping, time } = body;
  if (!userId || !date || !trackerIdDeviceIdMapping || !time) {
    return {
      statusCode: 400,
      body: JSON.stringify({
        success: false,
        message: "Required params missing",
        data: null,
      }),
      headers: defaultHeaders,
    };
  }
  
  await createOSClient();
  await connectMongoDB();
  const targetAchievementResponse = {};

  const UTCOffsetMin = await getUTCOffsetValue(userId);
  const { startTime, endTime } = getTimeRangeByDate(UTCOffsetMin, date);
  logger.info(`date: ${date} | UTCOffsetMin: ${UTCOffsetMin} | startTime: ${startTime}, endTime: ${endTime}`);
  
  for(const trackerId in trackerIdDeviceIdMapping){
    const deviceId = trackerIdDeviceIdMapping[trackerId];
    const data = await computeTargetAchievement(userId, trackerId, deviceId, date, startTime, endTime);
    targetAchievementResponse[trackerId] = data;
  }
  logger.info(`Response received: ${JSON.stringify(targetAchievementResponse)}`);

  const nudgeResponse =await calculateNudge(userId, UTCOffsetMin);
  logger.info(`NudgeResponse: ${JSON.stringify(nudgeResponse)}`);

  return {
    statusCode: 200,
    body: JSON.stringify({
      success: true,
      message: "Request processed successfully",
      data: { targetAchievementResponse, nudgeResponse },
    }),
    headers: defaultHeaders,
  };
};

async function calculateNudge(userId, UTCOffsetMin) {
  const userData = { userId };
  const rules = await instantiateRules();
  const engine = new NudgeEngine(rules);

  const date = new Date(); // current date e.g. 2025-06-05T10:45:00.000Z
  const endDate = getLocalDateString(date.toISOString(), UTCOffsetMin); // 2025-06-05
  const startDate = getAdjustedDate(endDate, -6);// 2025-05-30

  const activeWindowData = await getActiveWindowData(userId); // e.g. 2025-06-02 & 2025-06-08
  const { activeWindowStartDate, activeWindowEndDate } = activeWindowData || {};
  const prevActiveWindowStartDate = getAdjustedDate(activeWindowStartDate, -7); // 2025-05-26
  const prevActiveWindowEndDate = getAdjustedDate(activeWindowEndDate, -7); // 2025-06-01

  // Loading default devices to fetch logs
  const defTrackers = await getAllDefaultTrackers(userId);
  const defaultDevices = defTrackers?.defaultDevices || [];
  const defaultDevicePerTrackerId = defaultDevices.reduce((acc, dev) => { acc[dev.trackerId] = dev.deviceId; return acc;}, {});

  const activeTargetIds = await getActiveTargetIds(userId);
  const allTargetAchievements = await getTargetsAchievedByDateRange(userId, prevActiveWindowStartDate, endDate, activeTargetIds, true);
  const formattedTargetAchievements = {};
  allTargetAchievements.forEach(achievement => {
    if(!formattedTargetAchievements[achievement.targetId]) formattedTargetAchievements[achievement.targetId] = [];
    formattedTargetAchievements[achievement.targetId].push(achievement);
  });
  userData.data = {
    defaultDevicePerTrackerId,
    formattedTargetAchievements,
    targetIds: {
      sleep: sleepTargetId,
      meallog: mealLogTargetId,
    },
    activeTargetIds,
    dateTimeData: {    
      startDate, 
      endDate,
      activeWindowStartDate,
      activeWindowEndDate,
      prevActiveWindowStartDate,
      prevActiveWindowEndDate
    },
    UTCOffsetMin
  };
  // Pass user's logs data like sleep, meallog, steps to the engine for evaluation
  const nudges = engine.evaluate(userData);
  logger.info(`Nudges received: ${JSON.stringify(nudges)}`);

  const nudgeResponse = {};
  for(const nudgeDoc of nudges){
    const docId = await upsertUserRecommendations(userId, nudgeDoc, nudgeDoc.type, nudgeDoc.category);
    nudgeResponse[docId] = nudgeDoc;
  }
  return nudgeResponse;
}

async function getActiveTargetIds(userId) {
  const excludeTargetIds = [41, 42, 43, 44]; // These targetIds should be excluded as they are dummy
  const targetsMap = await getStaticTargetsMap();
  const targetIds = Object.keys(targetsMap).filter(targetId => (targetsMap[targetId].duration === 7 && !excludeTargetIds.includes(Number(targetId))));
  let latestTargetDetails = await Promise.all(targetIds.map(targetId => 
    getLatestTargetById(userId, targetId)
  ))
  latestTargetDetails = latestTargetDetails.filter((targetDetails) => targetDetails && targetDetails.isActive === true)
  const activeTargetIds = Object.keys(latestTargetDetails).map(Number);
  return activeTargetIds;
}
